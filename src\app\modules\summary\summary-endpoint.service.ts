import { Injectable } from '@angular/core';
import { environment } from '@environments/environment';
import { Observable } from 'rxjs';
import {
  SeHttpRequest,
  SeHttpResponse,
  SeHttpService,
} from 'se-ui-components-mf-lib';
import { Autoliquidacio, SummaryResponse } from './summary.model';

@Injectable({
  providedIn: 'root',
})
export class SummaryEndpointService {
  constructor(private httpService: SeHttpService) {
    // Empty constructor
  }

  getSummaryData(
    idTramit: string,
  ): Observable<SeHttpResponse<SummaryResponse>> {
    return this.httpService.get({
      method: 'get',
      baseUrl: environment.baseUrlDeclaracionsInformatives,
      url: `/declaracio/${idTramit}/summary`,
      clearExceptions: true,
    });
  }

  postSelfAssesment(idTramit: string): Observable<SeHttpResponse<string>> {
    return this.httpService.post({
      method: 'post',
      baseUrl: environment.baseUrlDeclaracionsInformatives,
      url: `/declaracio/${idTramit}/presentacio`,
      clearExceptions: true,
    });
  }

  getStatusAutoliquidacio = (
    idAutoliquidacio: string,
  ): Observable<SeHttpResponse<Autoliquidacio>> => {
    const httpRequest: SeHttpRequest = {
      baseUrl: environment.baseUrlTributs,
      url: `/autoliquidacio/${idAutoliquidacio}`,
      spinner: false,
      method: 'get',
    };

    return this.httpService.get(httpRequest);
  };
}
