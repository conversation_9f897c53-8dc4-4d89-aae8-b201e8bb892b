import { TestBed } from '@angular/core/testing';
import { Router } from '@angular/router';
import { YearDeclarationGuard } from './year-declaration.guard';
import { StoreService } from '@core/services';
import { AppRoutes } from '@core/models/app-routes.enum';

describe('YearDeclarationGuard', () => {
  let guard: YearDeclarationGuard;
  let storeService: jasmine.SpyObj<StoreService>;
  let router: jasmine.SpyObj<Router>;

  beforeEach(() => {
    const storeServiceSpy = jasmine.createSpyObj('StoreService', [], {
      idTramit: null,
    });
    const routerSpy = jasmine.createSpyObj('Router', ['navigate']);

    TestBed.configureTestingModule({
      providers: [
        YearDeclarationGuard,
        { provide: StoreService, useValue: storeServiceSpy },
        { provide: Router, useValue: routerSpy },
      ],
    });

    guard = TestBed.inject(YearDeclarationGuard);
    storeService = TestBed.inject(StoreService) as jasmine.SpyObj<StoreService>;
    router = TestBed.inject(Router) as jasmine.SpyObj<Router>;
  });

  it('should be created', () => {
    expect(guard).toBeTruthy();
  });

  it('should allow access when idTramit exists', () => {
    // Arrange
    Object.defineProperty(storeService, 'idTramit', {
      get: () => 'test-id-tramit',
    });

    // Act
    const result = guard.canActivate();

    // Assert
    expect(result).toBe(true);
    expect(router.navigate).not.toHaveBeenCalled();
  });

  it('should redirect to PARTICIPANTS when idTramit does not exist', () => {
    // Arrange
    Object.defineProperty(storeService, 'idTramit', {
      get: () => null,
    });

    // Act
    const result = guard.canActivate();

    // Assert
    expect(result).toBe(false);
    expect(router.navigate).toHaveBeenCalledWith([AppRoutes.PARTICIPANTS]);
  });
});
