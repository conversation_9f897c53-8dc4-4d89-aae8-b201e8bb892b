import { TestBed } from '@angular/core/testing';
import { Router } from '@angular/router';
import { SummaryGuard } from './summary.guard';
import { StoreService } from '@core/services';
import { AppRoutes } from '@core/models/app-routes.enum';

describe('SummaryGuard', () => {
  let guard: SummaryGuard;
  let storeService: jasmine.SpyObj<StoreService>;
  let router: jasmine.SpyObj<Router>;

  beforeEach(() => {
    const storeServiceSpy = jasmine.createSpyObj('StoreService', [], {
      modelDescription: null,
      idTramit: null,
    });
    const routerSpy = jasmine.createSpyObj('Router', ['navigate']);

    TestBed.configureTestingModule({
      providers: [
        SummaryGuard,
        { provide: StoreService, useValue: storeServiceSpy },
        { provide: Router, useValue: routerSpy },
      ],
    });

    guard = TestBed.inject(SummaryGuard);
    storeService = TestBed.inject(StoreService) as jasmine.SpyObj<StoreService>;
    router = TestBed.inject(Router) as jasmine.SpyObj<Router>;
  });

  it('should be created', () => {
    expect(guard).toBeTruthy();
  });

  it('should allow access when modelDescription exists', () => {
    // Arrange
    Object.defineProperty(storeService, 'modelDescription', {
      get: () => 'Test Model Description',
    });

    // Act
    const result = guard.canActivate();

    // Assert
    expect(result).toBe(true);
    expect(router.navigate).not.toHaveBeenCalled();
  });

  it('should redirect to YEAR_DECLARATION when modelDescription does not exist but idTramit exists', () => {
    // Arrange
    Object.defineProperty(storeService, 'modelDescription', {
      get: () => null,
    });
    Object.defineProperty(storeService, 'idTramit', {
      get: () => 'test-id-tramit',
    });

    // Act
    const result = guard.canActivate();

    // Assert
    expect(result).toBe(false);
    expect(router.navigate).toHaveBeenCalledWith([AppRoutes.YEAR_DECLARATION]);
  });

  it('should redirect to PARTICIPANTS when neither modelDescription nor idTramit exist', () => {
    // Arrange
    Object.defineProperty(storeService, 'modelDescription', {
      get: () => null,
    });
    Object.defineProperty(storeService, 'idTramit', {
      get: () => null,
    });

    // Act
    const result = guard.canActivate();

    // Assert
    expect(result).toBe(false);
    expect(router.navigate).toHaveBeenCalledWith([AppRoutes.PARTICIPANTS]);
  });
});
