import { LazyElementsModule } from '@angular-extensions/elements';
import { CommonModule } from '@angular/common';
import { CUSTOM_ELEMENTS_SCHEMA, NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { RouterModule, Routes } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';

import {
  SeAlertModule,
  SeButtonModule,
  SeCheckboxModule,
  SeConfirmationMessageModule,
  SeHighlightRadioContainerModule,
  SeInputModule,
  SePanelModule,
  SeRadioModule,
  SeUserInfoBarModule,
  SpinnerComponent,
} from 'se-ui-components-mf-lib';
import { environment } from 'src/environments/environment';
import { ParticipantsComponent } from './participants.component';

const routes: Routes = [
  {
    path: '',
    component: ParticipantsComponent,
    data: {
      title: 'SE_DECINF_MF.APP_TITLE',
      stepId: 'REPOSITION_RESOURCE_DESKTOP_STEP1',
      stepperId: 'REPOSITION_RESOURCE_ENABLE',
    },
  },
];

@NgModule({
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  declarations: [ParticipantsComponent],
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    TranslateModule.forChild(),
    RouterModule.forChild(routes),
    LazyElementsModule.forFeature({
      elementConfigs: [
        {
          tag: 'mf-tributs-taxpayer',
          url: environment.mfTributsURL,
          loadingComponent: SpinnerComponent,
        },
      ],
    }),
    SeButtonModule,
    SeRadioModule,
    SeConfirmationMessageModule,
    SeUserInfoBarModule,
    SeInputModule,
    SeCheckboxModule,
    SeHighlightRadioContainerModule,
    SeAlertModule,
    SePanelModule,
  ],
})
export class ParticipantsModule {}
