import { TestBed } from '@angular/core/testing';
import { RouterTestingModule } from '@angular/router/testing';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { AppComponent } from './app.component';
import { HeaderInfoService } from './core/services/header-info.service';
import {
  SeAuthService,
  SeLoginService,
  SePageLayoutService,
} from 'se-ui-components-mf-lib';
import { StepperService } from '@core/services';

describe('AppComponent', () => {
  beforeEach(() =>
    TestBed.configureTestingModule({
      imports: [RouterTestingModule, TranslateModule.forRoot()],
      declarations: [AppComponent],
      schemas: [CUSTOM_ELEMENTS_SCHEMA],
      providers: [
        TranslateService,
        {
          provide: HeaderInfoService,
          useValue: {
            infoItems$: jasmine.createSpy().and.returnValue([]),
            tags$: jasmine.createSpy().and.returnValue([]),
          },
        },
        {
          provide: StepperService,
          useValue: jasmine.createSpyObj('StepperService', [
            'initializeAvailableRoutes',
          ]),
        },
        {
          provide: SeAuthService,
          useValue: jasmine.createSpyObj('SeAuthService', [
            'getSessionStorageUser',
          ]),
        },
        {
          provide: SeLoginService,
          useValue: jasmine.createSpyObj('SeLoginService', ['login']),
        },
        {
          provide: SePageLayoutService,
          useValue: {
            listenNavigation: jasmine.createSpy(),
            getCurrentSteps: jasmine.createSpy().and.returnValue([]),
            isElementVisible: true,
          },
        },
      ],
    }),
  );

  it('should create the app', () => {
    const fixture = TestBed.createComponent(AppComponent);
    const app = fixture.componentInstance;
    expect(app).toBeTruthy();
  });
});
