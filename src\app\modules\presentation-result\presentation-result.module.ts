import { CUSTOM_ELEMENTS_SCHEMA, NgModule } from '@angular/core';
import { PresentationResultComponent } from './presentation-result.component';
import { RouterModule, Routes } from '@angular/router';
import { CommonModule } from '@angular/common';
import { TranslateModule } from '@ngx-translate/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import {
  SeButtonModule,
  SePanelModule,
  SpinnerComponent,
} from 'se-ui-components-mf-lib';
import { LazyElementsModule } from '@angular-extensions/elements';
import { environment } from '@environments/environment';

const routes: Routes = [
  {
    path: '',
    component: PresentationResultComponent,
    data: {
      title: 'SE_DECINF_MF.APP_TITLE',
      isElementVisible: false,
    },
  },
];

@NgModule({
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  declarations: [PresentationResultComponent],
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    TranslateModule.forChild(),
    RouterModule.forChild(routes),
    LazyElementsModule.forFeature({
      elementConfigs: [
        {
          tag: 'mf-gestions-receipt',
          url: environment.mfGestionsUrl,
          loadingComponent: SpinnerComponent,
        },
      ],
    }),
    SeButtonModule,
    SePanelModule,
  ],
  exports: [],
  providers: [],
})
export class PresentationResultModule {}
