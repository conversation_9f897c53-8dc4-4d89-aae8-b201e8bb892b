import { Component } from '@angular/core';
import { Router } from '@angular/router';
import { AppRoutes } from '@core/models/app-routes.enum';
import { StoreService } from '@core/services';
import { TranslateService } from '@ngx-translate/core';
import { Nullable } from 'se-ui-components-mf-lib';

@Component({
  selector: 'app-presentation-result',
  templateUrl: './presentation-result.component.html',
  styleUrls: [],
})
export class PresentationResultComponent {
  protected resourceProcessReceiptId: Nullable<string> =
    this.storeService.numJustificant;
  protected resourceProcessReceiptName: Nullable<string> = `${this.storeService.numJustificant}.pdf`;
  protected linkATC: string = this.getLinkATC();

  constructor(
    private translateService: TranslateService,
    private router: Router,
    private storeService: StoreService,
  ) {
    // Empty constructor
  }

  private getLinkATC(): string {
    return `https://${this.translateService.instant('SE_DECINF_MF.MODULE_PRESENTATION_RESULT.LINK')}`;
  }

  protected startNewPresentation(): void {
    this.router.navigate([AppRoutes.PARTICIPANTS]);
  }
}
