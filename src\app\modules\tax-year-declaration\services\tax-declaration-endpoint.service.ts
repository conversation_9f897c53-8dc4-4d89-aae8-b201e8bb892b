import { Injectable } from '@angular/core';
import { environment } from '@environments/environment';
import { Observable } from 'rxjs';
import { SeHttpResponse, SeHttpService } from 'se-ui-components-mf-lib';
import {
  DeclarationTypes,
  GetTaxDeclarationData,
  PutTaxDeclarationData,
  Substitutiva,
  SubstitutivaData,
  ValidationProgress,
} from '../tax-year-declaration.model';

@Injectable({
  providedIn: 'root',
})
export class TaxYearDeclarationEndpointService {
  constructor(private httpService: SeHttpService) {
    // Empty constructor
  }

  getDeclarationData(
    idTramit: string,
  ): Observable<SeHttpResponse<GetTaxDeclarationData>> {
    return this.httpService.get({
      method: 'get',
      baseUrl: environment.baseUrlDeclaracionsInformatives,
      url: `/declaracio/${idTramit}`,
      clearExceptions: true,
    });
  }

  getDeclarationTypes(): Observable<SeHttpResponse<DeclarationTypes[]>> {
    return this.httpService.get({
      method: 'get',
      baseUrl: environment.baseUrlDeclaracionsInformatives,
      url: `/declaracio/model`,
      clearExceptions: true,
    });
  }

  getTaxYearsByImpost(model: string): Observable<SeHttpResponse<string[]>> {
    return this.httpService.get({
      method: 'get',
      baseUrl: environment.baseUrlDeclaracionsInformatives,
      url: `/declaracio/model/${model}/exercici`,
      clearExceptions: true,
    });
  }

  putTaxDeclarationData(
    body: PutTaxDeclarationData,
    idTramit: string,
  ): Observable<SeHttpResponse> {
    return this.httpService.put({
      method: 'put',
      baseUrl: environment.baseUrlDeclaracionsInformatives,
      url: `/declaracio/${idTramit}`,
      body,
      clearExceptions: true,
    });
  }

  postValidateTaxDeclaration(
    idTramit: string,
  ): Observable<SeHttpResponse<string>> {
    return this.httpService.post({
      method: 'post',
      baseUrl: environment.baseUrlDeclaracionsInformatives,
      url: `/declaracio/${idTramit}/validacio`,
      clearExceptions: true,
    });
  }

  getValidationStatus(
    idValidation: string,
  ): Observable<SeHttpResponse<ValidationProgress>> {
    return this.httpService.get({
      method: 'get',
      baseUrl: environment.baseUrlDeclaracionsInformatives,
      url: `/declaracio/validacio/${idValidation}`,
      clearExceptions: true,
    });
  }

  getDownloadErrorTemplate(
    idValidation: string,
  ): Observable<SeHttpResponse<string>> {
    return this.httpService.get({
      method: 'get',
      baseUrl: environment.baseUrlDeclaracionsInformatives,
      url: `/declaracio/validacio/${idValidation}/errors`,
      clearExceptions: true,
    });
  }

  patchCancelFileValidation(idValidation: string): Observable<SeHttpResponse> {
    return this.httpService.put({
      method: 'put',
      baseUrl: environment.baseUrlDeclaracionsInformatives,
      url: `/declaracio/validacio/${idValidation}/cancellar`,
      clearExceptions: true,
    });
  }

  searchManualSubstitutiva(
    idTramit: string,
    numJustificante: string,
    fechaPresentacion: string,
  ): Observable<SeHttpResponse<Substitutiva>> {
    return this.httpService.post({
      method: 'post',
      baseUrl: environment.baseUrlDeclaracionsInformatives,
      url: `/declaracio/${idTramit}/substitutiva`,
      clearExceptions: true,
      body: {
        numJustificante,
        fechaPresentacion,
      },
    });
  }

  getLastPresenterSubstitutiva(
    idTramit: string,
  ): Observable<SeHttpResponse<SubstitutivaData>> {
    return this.httpService.get({
      method: 'get',
      baseUrl: environment.baseUrlDeclaracionsInformatives,
      url: `/declaracio/${idTramit}/substitutiva`,
      clearExceptions: true,
    });
  }
}
