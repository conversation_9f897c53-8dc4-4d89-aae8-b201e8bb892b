import { Injectable } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { Column, SeModal } from 'se-ui-components-mf-lib';

@Injectable({
  providedIn: 'root',
})
export class TaxYearDeclarationService {
  constructor(private translateService: TranslateService) {
    // Empty constructor
  }

  private readonly modelUrls = new Map<
    string,
    { es: string | null; ca: string | null }
  >([
    [
      '523',
      {
        ca: 'https://atc.gencat.cat/web/.content/atc_tributs/ibee/Model-523.csv',
        es: 'https://atc.gencat.cat/web/.content/atc_tributs/ibee/Model-523-es.csv',
      },
    ],
    [
      '524',
      {
        ca: 'https://atc.gencat.cat/web/.content/atc_tributs/ibee/Model-524.csv',
        es: 'https://atc.gencat.cat/web/.content/atc_tributs/ibee/Model-524-es.csv',
      },
    ],
    [
      '525',
      {
        ca: 'https://atc.gencat.cat/web/.content/atc_tributs/ibee/Model-525.csv',
        es: 'https://atc.gencat.cat/web/.content/atc_tributs/ibee/Model-525-es.csv',
      },
    ],
    [
      '543',
      {
        ca: 'https://atc.gencat.cat/web/.content/documents/05_doc_models/decl-inf-anuals/csv543.csv',
        es: 'https://atc.gencat.cat/web/.content/documents/05_doc_models/decl-inf-anuals/csv543-es.csv',
      },
    ],
    [
      '643',
      {
        ca: 'https://atc.gencat.cat/web/.content/documents/05_doc_models/decl-inf-anuals/csv643.csv',
        es: 'https://atc.gencat.cat/web/.content/documents/05_doc_models/decl-inf-anuals/csv643-es.csv',
      },
    ],
    [
      '644',
      {
        ca: 'https://atc.gencat.cat/web/.content/documents/05_doc_models/decl-inf-anuals/csv644.csv',
        es: 'https://atc.gencat.cat/web/.content/documents/05_doc_models/decl-inf-anuals/csv644-es.csv',
      },
    ],
    [
      '645',
      {
        ca: 'https://atc.gencat.cat/web/.content/documents/05_doc_models/decl-inf-anuals/csv645.csv',
        es: 'https://atc.gencat.cat/web/.content/documents/05_doc_models/decl-inf-anuals/csv645-es.csv',
      },
    ],
    [
      '648',
      {
        ca: null,
        es: null,
      },
    ],
    [
      '673',
      {
        ca: 'https://atc.gencat.cat/web/.content/documents/05_doc_models/decl-inf-anuals/csv673.csv',
        es: 'https://atc.gencat.cat/web/.content/documents/05_doc_models/decl-inf-anuals/csv673-es.csv',
      },
    ],
    [
      '943',
      {
        ca: 'https://atc.gencat.cat/web/.content/documents/05_doc_models/decl-inf-anuals/csv943.csv',
        es: 'https://atc.gencat.cat/web/.content/documents/05_doc_models/decl-inf-anuals/csv943-es.csv',
      },
    ],
  ]);

  getModelUrl(model: string, idiom: 'es' | 'ca'): string | null {
    const modelData = this.modelUrls.get(model);
    return modelData ? modelData[idiom] : null;
  }

  getTableColumns(): Column[] {
    return [
      {
        header: 'SE_COMPONENTS.FILE_UPLOADER.NAME',
        key: 'name',
        cellComponentName: 'defaultCellComponent',
        resizable: false,
        cellConfig: {
          tooltip: true,
          ellipsis: true,
        },
      },
      {
        header: 'SE_COMPONENTS.FILE_UPLOADER.SIZE',
        key: 'size',
        cellComponentName: 'defaultCellComponent',
        resizable: false,
      },
      {
        header: 'UI_COMPONENTS.ATTACH_FILES.DOCUMENT_TYPE_LABEL',
        key: 'docType',
        cellComponentName: 'defaultCellComponent',
        resizable: false,
      },
      {
        header: 'SE_COMPONENTS.FILE_UPLOADER.DESCRIPTION',
        key: 'description',
        cellComponentName: 'defaultCellComponent',
        resizable: false,
      },
    ];
  }

  getSubstitutiveTableColumns(): Column[] {
    return [
      {
        header:
          'SE_DECINF_MF.MODULE_TAX_YEAR_DECLARATION.SUBSTITUTIVE_PANEL.TABLE.HEAD.JUSTIFICANT',
        key: 'numJustificant',
        cellComponentName: 'defaultCellComponent',
        resizable: false,
      },
      {
        header:
          'SE_DECINF_MF.MODULE_TAX_YEAR_DECLARATION.SUBSTITUTIVE_PANEL.TABLE.HEAD.DECLARANT',
        key: 'declarant',
        cellComponentName: 'defaultCellComponent',
        resizable: false,
      },
      {
        header:
          'SE_DECINF_MF.MODULE_TAX_YEAR_DECLARATION.SUBSTITUTIVE_PANEL.TABLE.HEAD.DATA',
        key: 'dataPresentacio',
        cellComponentName: 'dateCellComponent',
        resizable: false,
      },
      {
        header:
          'SE_DECINF_MF.MODULE_TAX_YEAR_DECLARATION.SUBSTITUTIVE_PANEL.TABLE.HEAD.STATE',
        key: 'estat',
        cellComponentName: 'defaultCellComponent',
        resizable: false,
      },
    ];
  }

  getModalTableColumns(): Column[] {
    return [
      {
        header: 'SE_COMPONENTS.FILE_UPLOADER.NAME',
        key: 'name',
        cellComponentName: 'defaultCellComponent',
        cellConfig: {
          tooltip: true,
          ellipsis: true,
        },
        resizable: false,
      },
      {
        header: 'SE_COMPONENTS.FILE_UPLOADER.SIZE',
        key: 'size',
        size: 10,
        cellComponentName: 'defaultCellComponent',
        resizable: false,
      },
      {
        header: 'UI_COMPONENTS.ATTACH_FILES.DOCUMENT_TYPE_LABEL',
        key: 'docType',
        cellComponentName: 'dropdownCellComponent',
        resizable: false,
        cellConfig: {
          id: 'docType',
          options: [
            {
              id: 1,
              label: this.translateService.instant(
                'SE_DECINF_MF.MODULE_TAX_YEAR_DECLARATION.FILE_UPLOAD_PANEL.NOM_DOCUMENT',
              ),
            },
          ],
          disabled: true,
          showClear: false,
          editable: false,
          autoSelect: true,
          readOnly: false,
          filter: false,
          optionLabel: 'label',
          optionValue: 'id',
          placeholder: 'UI_COMPONENTS.SELECT.PLACEHOLDER',
          required: true,
        },
      },
      {
        header: this.translateService.instant(
          'SE_DECINF_MF.MODULE_TAX_YEAR_DECLARATION.FILE_UPLOAD_PANEL.FIELD_DESCRIPTION',
        ),
        key: 'description',
        cellComponentName: 'inputCellComponent',
        resizable: false,
        cellConfig: {
          id: 'docDescription',
          optionLabel: 'label',
          optionValue: 'id',
          placeholder: 'UI_COMPONENTS.SELECT.PLACEHOLDER',
          required: false,
        },
      },
    ];
  }

  getWarningModalData(): SeModal {
    return {
      severity: 'warning',
      title: this.translateService.instant(
        'SE_DECINF_MF.MODULE_TAX_YEAR_DECLARATION.WARNING_MODAL.TITLE',
      ),
      subtitle: `<p>${this.translateService.instant(
        'SE_DECINF_MF.MODULE_TAX_YEAR_DECLARATION.WARNING_MODAL.DESCRIPTION',
      )}</p><p>${this.translateService.instant(
        'SE_DECINF_MF.MODULE_TAX_YEAR_DECLARATION.WARNING_MODAL.QUESTION',
      )}</p>`,
      secondaryButton: true,
      secondaryButtonLabel: this.translateService.instant(
        'UI_COMPONENTS.BUTTONS.CANCEL',
      ),
      closableLabel: this.translateService.instant(
        'UI_COMPONENTS.BUTTONS.CONTINUE',
      ),
      closable: true,
      size: 'lg',
    };
  }
}
