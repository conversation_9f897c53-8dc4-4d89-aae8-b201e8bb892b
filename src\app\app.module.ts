import { DOCUMENT, registerLocaleData } from '@angular/common';
import {
  HTTP_INTERCEPTORS,
  HttpBackend,
  HttpClientModule,
} from '@angular/common/http';
import localeCa from '@angular/common/locales/ca';
import {
  APP_INITIALIZER,
  ApplicationRef,
  DoBootstrap,
  Injector,
  LOCALE_ID,
  NgModule,
  isDevMode,
} from '@angular/core';
import { createCustomElement } from '@angular/elements';
import { BrowserModule } from '@angular/platform-browser';
import {
  TranslateLoader,
  TranslateModule,
  TranslateService,
} from '@ngx-translate/core';
import { CookieService } from 'ngx-cookie-service';
import { MultiTranslateHttpLoader } from 'ngx-translate-multi-http-loader';
import { PrimeNGConfig } from 'primeng/api';
import { lastValueFrom, take } from 'rxjs';
import {
  SeExceptionViewerModule,
  SeHeaderInfoModule,
  SeHttpInterceptorService,
  SeS<PERSON>perdModule,
  SeTableModule,
} from 'se-ui-components-mf-lib';
// App
import { environment } from 'src/environments/environment';
import { AppRoutingModule } from './app-routing.module';
import { AppComponent } from './app.component';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';

export function HttpLoaderFactory(
  httpBackend: HttpBackend,
): MultiTranslateHttpLoader {
  return new MultiTranslateHttpLoader(httpBackend, [
    { prefix: `${environment.baseUrlMf}/assets/i18n/`, suffix: '.json' },
    { prefix: `${environment.baseUrlCommons}/i18n/`, suffix: '.json' },
  ]);
}

export function appInitializerFactory(
  translate: TranslateService,
  document: Document,
  primeNGConfig: PrimeNGConfig,
): () => Promise<unknown> {
  return () => {
    translate.addLangs(['ca', 'es']);
    translate.setDefaultLang('ca');

    const currentLang = window.location.href.includes('/es/') ? 'es' : 'ca';
    document.documentElement.lang = currentLang;

    // Set primeNG specific translations
    translate
      .get('SE_COMPONENTS.PRIMENG')
      .pipe(take(1))
      .subscribe((res) => primeNGConfig.setTranslation(res));

    return lastValueFrom(translate.use(currentLang));
  };
}

registerLocaleData(localeCa, 'ca');

@NgModule({
  declarations: [AppComponent],
  imports: [
    BrowserModule,
    BrowserAnimationsModule,
    HttpClientModule,
    AppRoutingModule,
    TranslateModule.forRoot({
      loader: {
        provide: TranslateLoader,
        useFactory: HttpLoaderFactory,
        deps: [HttpBackend],
      },
    }),
    SeTableModule,
    SeExceptionViewerModule,
    SeStepperdModule,
    SeHeaderInfoModule,
  ],
  providers: [
    { provide: LOCALE_ID, useValue: 'ca-ES' },
    {
      provide: HTTP_INTERCEPTORS,
      useClass: SeHttpInterceptorService,
      multi: true,
    },
    CookieService,
    {
      provide: APP_INITIALIZER,
      useFactory: appInitializerFactory,
      deps: [TranslateService, DOCUMENT, PrimeNGConfig],
      multi: true,
    },
  ],
  bootstrap: [],
})
export class AppModule implements DoBootstrap {
  constructor(private injector: Injector) {}

  ngDoBootstrap(appRef: ApplicationRef): void {
    const el = createCustomElement(AppComponent, { injector: this.injector });

    // Same name inside concat elements-build.js
    if (!customElements.get('se-declaracions-informatives')) {
      customElements.define('se-declaracions-informatives', el);
    }

    if (isDevMode()) {
      appRef.bootstrap(AppComponent);
    }
  }
}
