<div class="d-flex flex-column gap-4">
  <form [formGroup]="componentForm" class="d-flex flex-column gap-4">
    <se-panel
      [id]="'tipus_declaracio_panel_id'"
      [title]="
        'SE_DECINF_MF.MODULE_TAX_YEAR_DECLARATION.TYPE_DECLARATION_PANEL.TITLE'
          | translate
      "
      [colapsible]="false"
      [collapsed]="false"
      [panelTheme]="'default'"
    >
      <p>
        {{
          'SE_DECINF_MF.MODULE_TAX_YEAR_DECLARATION.TYPE_DECLARATION_PANEL.DESCRIPTION'
            | translate
        }}
      </p>

      <div class="row">
        <se-dropdown
          class="col-12 col-md-10"
          [id]="'tipus_declaration_dropdown_id'"
          [label]="
            'SE_DECINF_MF.MODULE_TAX_YEAR_DECLARATION.TYPE_DECLARATION_PANEL.TITLE'
              | translate
          "
          [editable]="false"
          [options]="optionsModelList"
          [placeholder]="'UI_COMPONENTS.SELECT.PLACEHOLDER' | translate"
          formControlName="modelCode"
          (dropdownOutput)="onModelChange($event)"
        ></se-dropdown>
      </div>
    </se-panel>
    <se-panel
      [id]="'tax_year_panel_id'"
      [title]="
        'SE_DECINF_MF.MODULE_TAX_YEAR_DECLARATION.TAX_YEAR_PANEL.TITLE'
          | translate
      "
      [colapsible]="false"
      [collapsed]="false"
      [panelTheme]="'default'"
    >
      <p>
        {{
          'SE_DECINF_MF.MODULE_TAX_YEAR_DECLARATION.TAX_YEAR_PANEL.DESCRIPTION'
            | translate
        }}
      </p>
      <div class="row">
        <se-dropdown
          class="col-12 col-md-3"
          [id]="'exercici_dropdown_id'"
          [label]="
            'SE_DECINF_MF.MODULE_TAX_YEAR_DECLARATION.TAX_YEAR_PANEL.TITLE'
              | translate
          "
          [editable]="false"
          [options]="optionsTaxYearList"
          [placeholder]="'UI_COMPONENTS.SELECT.PLACEHOLDER' | translate"
          formControlName="exercici"
          (dropdownOutput)="onExerciciChange($event)"
        ></se-dropdown>
      </div>
    </se-panel>
    <ng-container *ngIf="showPanelCodeInsurance">
      <app-insurance-panel
        [componentForm]="componentForm"
        (dadaAdicionalSaved)="searchSubstituteiveDeclaration()"
      ></app-insurance-panel>
    </ng-container>

    <!-- SUBSTITUTIVE PANEL -->
    <ng-container *ngIf="substitutiveData?.showPanel">
      <app-substitutive-panel
        [taxYear]="componentForm.get('exercici')?.value"
        [substitutiva]="substitutiveData"
        (substitutiveChange)="onSubstitutiveChange($event)"
      ></app-substitutive-panel>
    </ng-container>

    <!-- UPLOAD DOCUMENTS PANEL -->
    <se-panel
      *ngIf="showPanelFileUpload"
      [id]="'upload_documents_panel_id'"
      [title]="fileUploadPanelTitle"
      [colapsible]="false"
      [collapsed]="false"
      [panelTheme]="'default'"
    >
      <se-alert
        *ngIf="showValidateFileErrorsId"
        [type]="'error'"
        [closeButton]="true"
      >
        <p>
          {{
            'SE_DECINF_MF.MODULE_TAX_YEAR_DECLARATION.PROGRESS_MODAL.ERROR.TITLE_1'
              | translate
          }}
          <se-button
            class="alert-link"
            [btnTheme]="'trueOnlyText'"
            (onClick)="downloadFileErrors()"
          >
            {{
              'SE_DECINF_MF.MODULE_TAX_YEAR_DECLARATION.PROGRESS_MODAL.ERROR.LINK_TEXT'
                | translate
            }}
          </se-button>
          {{
            'SE_DECINF_MF.MODULE_TAX_YEAR_DECLARATION.PROGRESS_MODAL.ERROR.TITLE_2'
              | translate
          }}
        </p>
      </se-alert>
      <p>
        {{
          'SE_DECINF_MF.MODULE_TAX_YEAR_DECLARATION.FILE_UPLOAD_PANEL.DESCRIPTION_1'
            | translate
        }}
      </p>
      <p [innerHTML]="downloadFileTemplateUrl"></p>
      <p>
        {{
          'SE_DECINF_MF.MODULE_TAX_YEAR_DECLARATION.FILE_UPLOAD_PANEL.DESCRIPTION_3'
            | translate
        }}
      </p>

      <!-- documentacion a aportar -->
      <mf-documents-upload-files
        *axLazyElement
        [id]="(idTramit || 'idTramit') + functionalModule"
        [hasActions]="true"
        [panelMode]="false"
        [key]="'0'"
        [idFunctionalModule]="functionalModule"
        [sigedaDescriptions]="contestableActDocument"
        [idEntity]="idTramit || 'idTramit'"
        [tableColumns]="tableColumns"
        [required]="true"
        [modalTableColumns]="modalTableColumns"
        [accept]="acceptedFiles"
        [panelDescription]="
          'SE_DECINF_MF.MODULE_TAX_YEAR_DECLARATION.FILE_UPLOAD_PANEL.UPLOAD_DESCRIPTION'
            | translate
        "
        [dropAreaTitlePreLinkText]="
          'SE_COMPONENTS.FILE_UPLOADER.ARROSSEGUEU' | translate
        "
        [dropAreaTitleLinkText]="
          'SE_COMPONENTS.FILE_UPLOADER.CLICK' | translate
        "
        [dropAreaTitlePostLinkText]="
          'SE_COMPONENTS.FILE_UPLOADER.CARGAR' | translate
        "
        [multiple]="false"
        [maxFiles]="1"
        [deleteFileByDocId$]="deleteFileByDocId$"
        (addedFiles)="onFilesLoaded($event)"
      >
      </mf-documents-upload-files>
    </se-panel>
  </form>

  <!--  BUTTONS -->
  <section class="d-flex justify-content-between flex-row">
    <se-button (onClick)="goBack()" [btnTheme]="'secondary'">
      {{ 'UI_COMPONENTS.BUTTONS.PREVIOUS' | translate }}
    </se-button>
    <se-button
      (onClick)="validateBeforeSubmit()"
      [disabled]="!componentForm.valid"
    >
      {{ 'UI_COMPONENTS.BUTTONS.NEXT' | translate }}
    </se-button>
  </section>
</div>
